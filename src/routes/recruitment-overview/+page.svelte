<script lang="ts">
  import { onMount } from 'svelte';
  import { Button } from '$lib/components/ui/button';
  import {
    Download,
    Building2,
    DollarSign,
    ChevronLeft,
    ChevronRight,
    ArrowUpDown,
    FileText,
    SlidersHorizontal,
    Plus,
    Edit,
    Trash2
  } from 'lucide-svelte';
  import * as Tooltip from "$lib/components/ui/tooltip";
  import {
    recruitmentPolicyService,
    type RecruitmentPolicyOverviewQuery,
    type RecruitmentPolicyOverviewPagination,
    type RecruitmentPolicyWithProject,
    type DictionaryItem
  } from '$lib/services/recruitmentPolicyService';
  import { sqliteDictionaryService } from '$lib/services/sqliteDictionaryService';
  import { projectManagementService } from '$lib/services/projectManagementService';
  import PolicyFormModal from '$lib/components/recruitment/PolicyFormModal.svelte';

  // 页面状态
  let isLoading = false;
  let error: string | null = null;
  let successMessage: string | null = null;

  // 数据
  let policyData: RecruitmentPolicyOverviewPagination = {
    items: [],
    total: 0,
    page: 1,
    page_size: 20
  };
  let projectStages: DictionaryItem[] = [];
  let diseases: DictionaryItem[] = [];
  let recruitmentCompanies: DictionaryItem[] = [];

  // 查询参数
  let query: RecruitmentPolicyOverviewQuery = {
    page: 1,
    page_size: 20,
    sort_by: 'p.project_name',
    sort_order: 'asc',
    is_active: true
  };

  // 筛选状态
  let showFilters = false;
  let selectedStages: number[] = [];
  let selectedDiseases: number[] = [];
  let selectedCompanies: number[] = [];

  // 编辑和删除状态
  let showEditModal = false;
  let showCreateModal = false;
  let showDeleteConfirm = false;
  let editingPolicy: RecruitmentPolicyWithProject | null = null;
  let deletingPolicyId: number | null = null;

  // 项目列表（用于新增政策时选择）
  let allProjects: any[] = [];



  // 加载数据
  async function loadData() {
    isLoading = true;
    error = null;

    try {
      // 初始化表
      await recruitmentPolicyService.initTables();
      await recruitmentPolicyService.migrateTables();

      // 并行加载筛选选项和政策数据
      const [stageDict, diseaseDict, companiesData, projectsData, policiesData] = await Promise.all([
        sqliteDictionaryService.getDictByName('研究阶段'),
        sqliteDictionaryService.getDictByName('疾病'),
        recruitmentPolicyService.getRecruitmentCompanies(),
        projectManagementService.getProjects({ page_size: 1000 }), // 获取所有项目
        recruitmentPolicyService.getPoliciesOverview(query)
      ]);

      // 获取字典项
      const [stagesData, diseasesData] = await Promise.all([
        sqliteDictionaryService.getDictItems(stageDict.id!),
        sqliteDictionaryService.getDictItems(diseaseDict.id!)
      ]);

      // 转换数据格式
      projectStages = stagesData.map(item => ({
        item_id: item.item_id!,
        item_key: item.key,
        item_value: item.value,
        item_description: item.description
      }));
      diseases = diseasesData.map(item => ({
        item_id: item.item_id!,
        item_key: item.key,
        item_value: item.value,
        item_description: item.description
      }));
      recruitmentCompanies = companiesData;
      allProjects = projectsData.items; // 从分页结果中提取项目列表
      policyData = policiesData;

      console.log('加载数据成功:', { 
        stages: projectStages.length, 
        diseases: diseases.length, 
        companies: recruitmentCompanies.length,
        policies: policyData.items.length,
        total: policyData.total
      });
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
      console.error('加载数据失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 应用筛选
  async function applyFilters() {
    query = {
      ...query,
      page: 1,
      project_stage_item_id: selectedStages.length > 0 ? selectedStages[0] : undefined,
      disease_item_id: selectedDiseases.length > 0 ? selectedDiseases[0] : undefined,
      recruitment_company_item_id: selectedCompanies.length > 0 ? selectedCompanies[0] : undefined
    };
    await loadPolicies();
  }

  // 清除筛选
  async function clearFilters() {
    selectedStages = [];
    selectedDiseases = [];
    selectedCompanies = [];
    query = {
      page: 1,
      page_size: 20,
      sort_by: 'p.project_name',
      sort_order: 'asc',
      is_active: true
    };
    await loadPolicies();
  }

  // 加载政策数据
  async function loadPolicies() {
    isLoading = true;
    error = null;

    try {
      const result = await recruitmentPolicyService.getPoliciesOverview(query);
      policyData = result;
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
      console.error('加载政策数据失败:', err);
    } finally {
      isLoading = false;
    }
  }

  // 分页处理
  async function goToPage(page: number) {
    query = { ...query, page };
    await loadPolicies();
  }

  // 排序处理
  async function sortBy(field: string) {
    const newOrder = query.sort_by === field && query.sort_order === 'asc' ? 'desc' : 'asc';
    query = { ...query, sort_by: field, sort_order: newOrder };
    await loadPolicies();
  }

  // 格式化费用
  function formatFee(amount: number, currency: string = 'CNY'): string {
    const symbol = currency === 'CNY' ? '¥' : currency === 'USD' ? '$' : currency === 'EUR' ? '€' : currency;
    return `${symbol}${amount.toLocaleString()}`;
  }

  // 计算费用差额
  function calculateFeeDifference(policy: RecruitmentPolicyWithProject): number {
    return policy.policy.randomization_fee - policy.policy.informed_consent_fee;
  }

  // 格式化日期
  function formatDate(dateString?: string): string {
    if (!dateString) return '-';
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch {
      return '-';
    }
  }

  // 导出数据
  async function exportData() {
    try {
      // 获取所有数据（不分页）
      const exportQuery = { ...query, page: 1, page_size: 10000 };
      const allData = await recruitmentPolicyService.getPoliciesOverview(exportQuery);
      
      // 转换为CSV格式
      const headers = [
        '项目名称', '项目简称', '疾病', '研究阶段', '项目状态', '招募状态',
        '招募公司', '政策名称', '知情费用', '随机费用', '费用差额', 
        '货币', '支付方式', '创建时间', '备注'
      ];
      
      const csvContent = [
        headers.join(','),
        ...allData.items.map(item => [
          `"${item.project_name}"`,
          `"${item.project_short_name}"`,
          `"${item.disease?.item_value || '-'}"`,
          `"${item.project_stage?.item_value || '-'}"`,
          `"${item.project_status?.item_value || '-'}"`,
          `"${item.recruitment_status?.item_value || '-'}"`,
          `"${item.company?.item_value || '-'}"`,
          `"${item.policy.policy_name || '-'}"`,
          item.policy.informed_consent_fee,
          item.policy.randomization_fee,
          calculateFeeDifference(item),
          `"${item.policy.fee_currency || 'CNY'}"`,
          `"${item.policy.payment_method || '-'}"`,
          `"${formatDate(item.policy.created_at)}"`,
          `"${item.policy.notes || '-'}"`
        ].join(','))
      ].join('\n');

      // 下载文件
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `招募政策概览_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      successMessage = '数据导出成功';
      setTimeout(() => successMessage = null, 3000);
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
      console.error('导出数据失败:', err);
    }
  }

  // 编辑政策
  function openEditModal(policy: RecruitmentPolicyWithProject) {
    editingPolicy = policy;
    showEditModal = true;
  }

  // 删除政策
  function openDeleteConfirm(policyId: number) {
    deletingPolicyId = policyId;
    showDeleteConfirm = true;
  }

  // 确认删除
  async function confirmDelete() {
    if (!deletingPolicyId) return;

    try {
      await recruitmentPolicyService.deletePolicy(deletingPolicyId);
      successMessage = '政策删除成功';
      setTimeout(() => successMessage = null, 3000);

      // 刷新数据
      await loadPolicies();
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
      console.error('删除政策失败:', err);
    } finally {
      showDeleteConfirm = false;
      deletingPolicyId = null;
    }
  }

  // 取消删除
  function cancelDelete() {
    showDeleteConfirm = false;
    deletingPolicyId = null;
  }

  // 关闭编辑模态框
  function closeEditModal() {
    showEditModal = false;
    editingPolicy = null;
  }

  // 打开新增政策模态框
  function openCreateModal() {
    showCreateModal = true;
  }

  // 关闭新增政策模态框
  function closeCreateModal() {
    showCreateModal = false;
  }

  // 保存新增政策
  async function saveCreate(newPolicy: any) {
    try {
      await recruitmentPolicyService.createPolicy(newPolicy);
      successMessage = '政策创建成功';
      setTimeout(() => successMessage = null, 3000);

      // 刷新数据
      await loadPolicies();
      closeCreateModal();
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
      console.error('创建政策失败:', err);
    }
  }

  // 保存编辑
  async function saveEdit(updatedPolicy: any) {
    try {
      if (!editingPolicy?.policy.policy_id) return;

      await recruitmentPolicyService.updatePolicy(editingPolicy.policy.policy_id, updatedPolicy);
      successMessage = '政策更新成功';
      setTimeout(() => successMessage = null, 3000);

      // 刷新数据
      await loadPolicies();
      closeEditModal();
    } catch (err) {
      error = err instanceof Error ? err.message : String(err);
      console.error('更新政策失败:', err);
    }
  }

  // 组件挂载时加载数据
  onMount(() => {
    loadData();
  });
</script>

<svelte:head>
  <title>招募政策概览 - PeckByte</title>
</svelte:head>

<div class="min-h-screen bg-gray-50">
  <!-- 页面头部 -->
  <div class="bg-white shadow-sm border-b border-gray-200">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="py-6">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900 flex items-center">
              <Building2 class="h-8 w-8 mr-3 text-blue-600" />
              招募政策概览
            </h1>
            <p class="mt-2 text-sm text-gray-600">
              查看所有项目的招募政策信息，支持按研究阶段、疾病类型等条件筛选
            </p>
          </div>
          <div class="flex space-x-3">
            <Button
              onclick={openCreateModal}
              class="bg-blue-600 hover:bg-blue-700 flex items-center"
            >
              <Plus class="h-4 w-4 mr-2" />
              新增政策
            </Button>
            <Button
              variant="outline"
              onclick={() => showFilters = !showFilters}
              class="flex items-center"
            >
              <SlidersHorizontal class="h-4 w-4 mr-2" />
              筛选
            </Button>
            <Button
              onclick={exportData}
              class="bg-green-600 hover:bg-green-700 flex items-center"
            >
              <Download class="h-4 w-4 mr-2" />
              导出
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 主要内容区域 -->
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- 成功/错误消息 -->
    {#if successMessage}
      <div class="mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-green-800">{successMessage}</p>
          </div>
        </div>
      </div>
    {/if}

    {#if error}
      <div class="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-red-800">{error}</p>
          </div>
        </div>
      </div>
    {/if}

    <!-- 筛选器 -->
    {#if showFilters}
      <div class="mb-6 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">筛选条件</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- 研究阶段筛选 -->
          <div>
            <label for="stage-filter" class="block text-sm font-medium text-gray-700 mb-2">研究阶段</label>
            <select
              id="stage-filter"
              bind:value={selectedStages[0]}
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value={undefined}>全部阶段</option>
              {#each projectStages as stage}
                <option value={stage.item_id}>{stage.item_value}</option>
              {/each}
            </select>
          </div>

          <!-- 疾病类型筛选 -->
          <div>
            <label for="disease-filter" class="block text-sm font-medium text-gray-700 mb-2">疾病类型</label>
            <select
              id="disease-filter"
              bind:value={selectedDiseases[0]}
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value={undefined}>全部疾病</option>
              {#each diseases as disease}
                <option value={disease.item_id}>{disease.item_value}</option>
              {/each}
            </select>
          </div>

          <!-- 招募公司筛选 -->
          <div>
            <label for="company-filter" class="block text-sm font-medium text-gray-700 mb-2">招募公司</label>
            <select
              id="company-filter"
              bind:value={selectedCompanies[0]}
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value={undefined}>全部公司</option>
              {#each recruitmentCompanies as company}
                <option value={company.item_id}>{company.item_value}</option>
              {/each}
            </select>
          </div>
        </div>

        <div class="mt-6 flex justify-end space-x-3">
          <Button variant="outline" onclick={clearFilters}>
            清除筛选
          </Button>
          <Button onclick={applyFilters} class="bg-blue-600 hover:bg-blue-700">
            应用筛选
          </Button>
        </div>
      </div>
    {/if}

    <!-- 统计信息 -->
    <div class="mb-6 grid grid-cols-1 md:grid-cols-4 gap-6">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <FileText class="h-8 w-8 text-blue-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">总政策数</p>
            <p class="text-2xl font-bold text-gray-900">{policyData.total}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <Building2 class="h-8 w-8 text-green-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">招募公司数</p>
            <p class="text-2xl font-bold text-gray-900">{recruitmentCompanies.length}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <DollarSign class="h-8 w-8 text-yellow-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">平均知情费用</p>
            <p class="text-2xl font-bold text-gray-900">
              {#if policyData.items.length > 0}
                ¥{Math.round(policyData.items.reduce((sum, item) => sum + item.policy.informed_consent_fee, 0) / policyData.items.length).toLocaleString()}
              {:else}
                ¥0
              {/if}
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <DollarSign class="h-8 w-8 text-red-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">平均随机费用</p>
            <p class="text-2xl font-bold text-gray-900">
              {#if policyData.items.length > 0}
                ¥{Math.round(policyData.items.reduce((sum, item) => sum + item.policy.randomization_fee, 0) / policyData.items.length).toLocaleString()}
              {:else}
                ¥0
              {/if}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold text-gray-800">招募政策列表</h3>
          <div class="text-sm text-gray-500">
            显示 {(policyData.page - 1) * policyData.page_size + 1} - {Math.min(policyData.page * policyData.page_size, policyData.total)} 条，共 {policyData.total} 条记录
          </div>
        </div>
      </div>

      {#if isLoading}
        <div class="flex items-center justify-center py-12">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span class="ml-3 text-gray-600">加载中...</span>
        </div>
      {:else if policyData.items.length > 0}
        <div class="overflow-x-auto">
          <table class="w-full">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-4 text-left text-sm font-semibold text-gray-700 border-b border-gray-200">
                  <button
                    onclick={() => sortBy('p.project_name')}
                    class="flex items-center hover:text-blue-600"
                  >
                    项目名称
                    <ArrowUpDown class="h-4 w-4 ml-1" />
                  </button>
                </th>
                <th class="px-6 py-4 text-left text-sm font-semibold text-gray-700 border-b border-gray-200">疾病</th>
                <th class="px-6 py-4 text-left text-sm font-semibold text-gray-700 border-b border-gray-200">研究阶段</th>
                <th class="px-6 py-4 text-left text-sm font-semibold text-gray-700 border-b border-gray-200">招募公司</th>
                <th class="px-6 py-4 text-left text-sm font-semibold text-gray-700 border-b border-gray-200">政策名称</th>
                <th class="px-6 py-4 text-center text-sm font-semibold text-gray-700 border-b border-gray-200">
                  <button
                    onclick={() => sortBy('rcp.informed_consent_fee')}
                    class="flex items-center hover:text-blue-600"
                  >
                    知情费用
                    <ArrowUpDown class="h-4 w-4 ml-1" />
                  </button>
                </th>
                <th class="px-6 py-4 text-center text-sm font-semibold text-gray-700 border-b border-gray-200">
                  <button
                    onclick={() => sortBy('rcp.randomization_fee')}
                    class="flex items-center hover:text-blue-600"
                  >
                    随机费用
                    <ArrowUpDown class="h-4 w-4 ml-1" />
                  </button>
                </th>
                <th class="px-6 py-4 text-center text-sm font-semibold text-gray-700 border-b border-gray-200">费用差额</th>
                <th class="px-6 py-4 text-center text-sm font-semibold text-gray-700 border-b border-gray-200">支付方式</th>
                <th class="px-6 py-4 text-center text-sm font-semibold text-gray-700 border-b border-gray-200">
                  <button
                    onclick={() => sortBy('rcp.created_at')}
                    class="flex items-center hover:text-blue-600"
                  >
                    创建时间
                    <ArrowUpDown class="h-4 w-4 ml-1" />
                  </button>
                </th>
                <th class="px-6 py-4 text-center text-sm font-semibold text-gray-700 border-b border-gray-200">操作</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              {#each policyData.items as item}
                <tr class="hover:bg-gray-50 transition-colors">
                  <td class="px-6 py-4">
                    <div>
                      <div class="font-medium text-gray-900">{item.project_name}</div>
                      <div class="text-sm text-gray-500">{item.project_short_name}</div>
                    </div>
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-900">
                    {item.disease?.item_value || '-'}
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-900">
                    {item.project_stage?.item_value || '-'}
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-900">
                    {item.company?.item_value || '-'}
                  </td>
                  <td class="px-6 py-4 text-sm text-gray-900">
                    {item.policy.policy_name || '-'}
                  </td>
                  <td class="px-6 py-4 text-center">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                      {formatFee(item.policy.informed_consent_fee, item.policy.fee_currency)}
                    </span>
                  </td>
                  <td class="px-6 py-4 text-center">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                      {formatFee(item.policy.randomization_fee, item.policy.fee_currency)}
                    </span>
                  </td>
                  <td class="px-6 py-4 text-center">
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                      {formatFee(calculateFeeDifference(item), item.policy.fee_currency)}
                    </span>
                  </td>
                  <td class="px-6 py-4 text-center text-sm text-gray-600">
                    {item.policy.payment_method || '-'}
                  </td>
                  <td class="px-6 py-4 text-center text-sm text-gray-500">
                    {formatDate(item.policy.created_at)}
                  </td>
                  <td class="px-6 py-4 text-center">
                    <div class="flex items-center justify-center space-x-2">
                      <Tooltip.Root>
                        <Tooltip.Trigger asChild let:builder>
                          <Button
                            builders={[builder]}
                            variant="ghost"
                            size="sm"
                            onclick={() => openEditModal(item)}
                            class="h-8 w-8 p-0 hover:bg-blue-50 hover:text-blue-600"
                          >
                            <Edit class="h-4 w-4" />
                          </Button>
                        </Tooltip.Trigger>
                        <Tooltip.Content>
                          <p>编辑政策</p>
                        </Tooltip.Content>
                      </Tooltip.Root>

                      <Tooltip.Root>
                        <Tooltip.Trigger asChild let:builder>
                          <Button
                            builders={[builder]}
                            variant="ghost"
                            size="sm"
                            onclick={() => openDeleteConfirm(item.policy.policy_id!)}
                            class="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600"
                          >
                            <Trash2 class="h-4 w-4" />
                          </Button>
                        </Tooltip.Trigger>
                        <Tooltip.Content>
                          <p>删除政策</p>
                        </Tooltip.Content>
                      </Tooltip.Root>
                    </div>
                  </td>
                </tr>
              {/each}
            </tbody>
          </table>
        </div>

        <!-- 分页 -->
        <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
          <div class="text-sm text-gray-700">
            第 {policyData.page} 页，共 {Math.ceil(policyData.total / policyData.page_size)} 页
          </div>
          <div class="flex space-x-2">
            <Button
              variant="outline"
              onclick={() => goToPage(policyData.page - 1)}
              disabled={policyData.page <= 1}
              class="flex items-center"
            >
              <ChevronLeft class="h-4 w-4 mr-1" />
              上一页
            </Button>
            <Button
              variant="outline"
              onclick={() => goToPage(policyData.page + 1)}
              disabled={policyData.page >= Math.ceil(policyData.total / policyData.page_size)}
              class="flex items-center"
            >
              下一页
              <ChevronRight class="h-4 w-4 ml-1" />
            </Button>
          </div>
        </div>
      {:else}
        <div class="text-center py-12">
          <Building2 class="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 mb-2">暂无招募政策</h3>
          <p class="text-gray-500">当前筛选条件下没有找到招募政策数据</p>
        </div>
      {/if}
    </div>
  </div>
</div>

<!-- 删除确认对话框 -->
{#if showDeleteConfirm}
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
      <div class="flex items-center mb-4">
        <div class="flex-shrink-0">
          <Trash2 class="h-6 w-6 text-red-600" />
        </div>
        <div class="ml-3">
          <h3 class="text-lg font-medium text-gray-900">确认删除</h3>
        </div>
      </div>
      <p class="text-sm text-gray-500 mb-6">
        您确定要删除这个招募政策吗？此操作不可撤销。
      </p>
      <div class="flex justify-end space-x-3">
        <Button variant="outline" onclick={cancelDelete}>
          取消
        </Button>
        <Button onclick={confirmDelete} class="bg-red-600 hover:bg-red-700">
          确认删除
        </Button>
      </div>
    </div>
  </div>
{/if}

<!-- 新增政策模态框 -->
<PolicyFormModal
  show={showCreateModal}
  mode="create"
  projects={allProjects}
  recruitmentCompanies={recruitmentCompanies}
  on:close={closeCreateModal}
  on:save={(event) => saveCreate(event.detail)}
/>

<!-- 编辑政策模态框 -->
<PolicyFormModal
  show={showEditModal}
  mode="edit"
  editingPolicy={editingPolicy}
  projects={allProjects}
  recruitmentCompanies={recruitmentCompanies}
  on:close={closeEditModal}
  on:save={(event) => saveEdit(event.detail)}
/>
