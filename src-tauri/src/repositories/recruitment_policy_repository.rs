use log::{error, info};
use rusqlite::{params, Connection, Result as SqliteResult};
use std::error::Error;

use crate::models::recruitment_policy::{
    CreateRecruitmentPolicyRequest, DictionaryItem, RecruitmentPolicy,
    RecruitmentPolicyQuery, RecruitmentPolicyWithCompany, UpdateRecruitmentPolicyRequest,
    RecruitmentPolicyOverviewQuery, RecruitmentPolicyWithProject, RecruitmentPolicyOverviewPagination,
};

/// 招募政策仓储层
pub struct RecruitmentPolicyRepository {
    db_path: String,
}

impl RecruitmentPolicyRepository {
    /// 创建新的仓储实例
    pub fn new(db_path: String) -> Self {
        Self { db_path }
    }

    /// 获取数据库连接
    fn get_connection(&self) -> Result<Connection, Box<dyn Error>> {
        let conn = Connection::open(&self.db_path)?;
        Ok(conn)
    }

    /// 初始化表（如果需要）
    pub fn init_tables(&self) -> Result<(), Box<dyn Error>> {
        let conn = self.get_connection()?;

        // 检查表是否存在
        let table_exists = conn
            .prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='recruitment_company_policies'")?
            .exists([])?;

        if !table_exists {
            info!("创建 recruitment_company_policies 表");
            conn.execute(
                "CREATE TABLE recruitment_company_policies (
                    policy_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    project_id TEXT NOT NULL,
                    recruitment_company_item_id INTEGER NOT NULL,
                    policy_name TEXT,
                    informed_consent_fee REAL NOT NULL DEFAULT 0.0,
                    randomization_fee REAL NOT NULL DEFAULT 0.0,
                    fee_currency TEXT DEFAULT 'CNY',
                    payment_method TEXT,
                    description TEXT,
                    is_active BOOLEAN NOT NULL DEFAULT 1,
                    notes TEXT,
                    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (project_id) REFERENCES projects(project_id),
                    FOREIGN KEY (recruitment_company_item_id) REFERENCES dictionary_items(item_id)
                )",
                [],
            )?;
        } else {
            // 执行数据库迁移以支持多个政策
            self.migrate_table_for_multiple_policies()?;
        }

        Ok(())
    }

    /// 迁移表结构以支持多个政策
    fn migrate_table_for_multiple_policies(&self) -> Result<(), Box<dyn Error>> {
        let conn = self.get_connection()?;

        // 检查是否存在 UNIQUE 约束
        let constraint_exists = conn
            .prepare("SELECT sql FROM sqlite_master WHERE type='table' AND name='recruitment_company_policies'")?
            .query_row([], |row| {
                let sql: String = row.get(0)?;
                Ok(sql.contains("UNIQUE (project_id, recruitment_company_item_id)"))
            })?;

        if constraint_exists {
            info!("检测到旧的 UNIQUE 约束，开始迁移表结构");

            // 开始事务
            let tx = conn.unchecked_transaction()?;

            // 1. 创建新表（不包含 UNIQUE 约束）
            tx.execute(
                "CREATE TABLE recruitment_company_policies_new (
                    policy_id INTEGER PRIMARY KEY AUTOINCREMENT,
                    project_id TEXT NOT NULL,
                    recruitment_company_item_id INTEGER NOT NULL,
                    policy_name TEXT,
                    informed_consent_fee REAL NOT NULL DEFAULT 0.0,
                    randomization_fee REAL NOT NULL DEFAULT 0.0,
                    fee_currency TEXT DEFAULT 'CNY',
                    payment_method TEXT,
                    description TEXT,
                    is_active BOOLEAN NOT NULL DEFAULT 1,
                    notes TEXT,
                    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (project_id) REFERENCES projects(project_id),
                    FOREIGN KEY (recruitment_company_item_id) REFERENCES dictionary_items(item_id)
                )",
                [],
            )?;

            // 2. 复制数据到新表
            tx.execute(
                "INSERT INTO recruitment_company_policies_new
                 (policy_id, project_id, recruitment_company_item_id, policy_name,
                  informed_consent_fee, randomization_fee, fee_currency, payment_method,
                  description, is_active, notes, created_at, updated_at)
                 SELECT policy_id, project_id, recruitment_company_item_id,
                        CASE
                            WHEN description IS NOT NULL AND description != '' THEN description
                            ELSE '默认政策'
                        END as policy_name,
                        informed_consent_fee, randomization_fee, fee_currency, payment_method,
                        description, is_active, notes, created_at, updated_at
                 FROM recruitment_company_policies",
                [],
            )?;

            // 3. 删除旧表
            tx.execute("DROP TABLE recruitment_company_policies", [])?;

            // 4. 重命名新表
            tx.execute(
                "ALTER TABLE recruitment_company_policies_new RENAME TO recruitment_company_policies",
                [],
            )?;

            // 提交事务
            tx.commit()?;

            info!("表结构迁移完成，已移除 UNIQUE 约束并添加 policy_name 字段");
        } else {
            // 检查是否需要添加 policy_name 字段
            let has_policy_name = conn
                .prepare("PRAGMA table_info(recruitment_company_policies)")?
                .query_map([], |row| {
                    let column_name: String = row.get(1)?;
                    Ok(column_name)
                })?
                .collect::<Result<Vec<_>, _>>()?
                .iter()
                .any(|name| name == "policy_name");

            if !has_policy_name {
                info!("添加 policy_name 字段");
                conn.execute(
                    "ALTER TABLE recruitment_company_policies ADD COLUMN policy_name TEXT",
                    [],
                )?;

                // 为现有记录设置默认的 policy_name
                conn.execute(
                    "UPDATE recruitment_company_policies
                     SET policy_name = CASE
                         WHEN description IS NOT NULL AND description != '' THEN description
                         ELSE '默认政策'
                     END
                     WHERE policy_name IS NULL",
                    [],
                )?;
            }
        }

        Ok(())
    }

    /// 获取项目的招募政策列表
    pub fn get_project_policies(
        &self,
        project_id: &str,
    ) -> Result<Vec<RecruitmentPolicyWithCompany>, Box<dyn Error>> {
        let conn = self.get_connection()?;

        let mut stmt = conn.prepare(
            "SELECT
                rcp.policy_id, rcp.project_id, rcp.recruitment_company_item_id, rcp.policy_name,
                rcp.informed_consent_fee, rcp.randomization_fee, rcp.fee_currency,
                rcp.payment_method, rcp.description, rcp.is_active, rcp.notes,
                rcp.created_at, rcp.updated_at,
                di.item_id, di.item_key, di.item_value, di.item_description
             FROM recruitment_company_policies rcp
             LEFT JOIN dictionary_items di ON rcp.recruitment_company_item_id = di.item_id
             WHERE rcp.project_id = ? AND rcp.is_active = 1
             ORDER BY di.item_value, rcp.policy_name",
        )?;

        let policy_iter = stmt.query_map(params![project_id], |row| {
            let policy = RecruitmentPolicy {
                policy_id: row.get(0)?,
                project_id: row.get(1)?,
                recruitment_company_item_id: row.get(2)?,
                policy_name: row.get(3)?,
                informed_consent_fee: row.get(4)?,
                randomization_fee: row.get(5)?,
                fee_currency: row.get(6)?,
                payment_method: row.get(7)?,
                description: row.get(8)?,
                is_active: row.get(9)?,
                notes: row.get(10)?,
                created_at: row.get(11)?,
                updated_at: row.get(12)?,
            };

            let company = if let Ok(item_id) = row.get::<_, i64>(13) {
                Some(DictionaryItem {
                    item_id,
                    item_key: row.get(14)?,
                    item_value: row.get(15)?,
                    item_description: row.get(16)?,
                })
            } else {
                None
            };

            Ok(RecruitmentPolicyWithCompany::new(policy, company))
        })?;

        let mut policies = Vec::new();
        for policy in policy_iter {
            policies.push(policy?);
        }

        info!("获取到 {} 个招募政策", policies.len());
        Ok(policies)
    }

    /// 根据查询条件获取招募政策
    pub fn get_policies(
        &self,
        query: &RecruitmentPolicyQuery,
    ) -> Result<Vec<RecruitmentPolicyWithCompany>, Box<dyn Error>> {
        let conn = self.get_connection()?;

        let mut sql = String::from(
            "SELECT
                rcp.policy_id, rcp.project_id, rcp.recruitment_company_item_id, rcp.policy_name,
                rcp.informed_consent_fee, rcp.randomization_fee, rcp.fee_currency,
                rcp.payment_method, rcp.description, rcp.is_active, rcp.notes,
                rcp.created_at, rcp.updated_at,
                di.item_id, di.item_key, di.item_value, di.item_description
             FROM recruitment_company_policies rcp
             LEFT JOIN dictionary_items di ON rcp.recruitment_company_item_id = di.item_id
             WHERE 1=1",
        );

        let mut params: Vec<Box<dyn rusqlite::ToSql>> = Vec::new();

        if let Some(project_id) = &query.project_id {
            sql.push_str(" AND rcp.project_id = ?");
            params.push(Box::new(project_id.clone()));
        }

        if let Some(company_id) = query.recruitment_company_item_id {
            sql.push_str(" AND rcp.recruitment_company_item_id = ?");
            params.push(Box::new(company_id));
        }

        if let Some(is_active) = query.is_active {
            sql.push_str(" AND rcp.is_active = ?");
            params.push(Box::new(is_active));
        }

        sql.push_str(" ORDER BY di.item_value, rcp.policy_name");

        let mut stmt = conn.prepare(&sql)?;
        let param_refs: Vec<&dyn rusqlite::ToSql> = params.iter().map(|p| p.as_ref()).collect();

        let policy_iter = stmt.query_map(&param_refs[..], |row| {
            let policy = RecruitmentPolicy {
                policy_id: row.get(0)?,
                project_id: row.get(1)?,
                recruitment_company_item_id: row.get(2)?,
                policy_name: row.get(3)?,
                informed_consent_fee: row.get(4)?,
                randomization_fee: row.get(5)?,
                fee_currency: row.get(6)?,
                payment_method: row.get(7)?,
                description: row.get(8)?,
                is_active: row.get(9)?,
                notes: row.get(10)?,
                created_at: row.get(11)?,
                updated_at: row.get(12)?,
            };

            let company = if let Ok(item_id) = row.get::<_, i64>(13) {
                Some(DictionaryItem {
                    item_id,
                    item_key: row.get(14)?,
                    item_value: row.get(15)?,
                    item_description: row.get(16)?,
                })
            } else {
                None
            };

            Ok(RecruitmentPolicyWithCompany::new(policy, company))
        })?;

        let mut policies = Vec::new();
        for policy in policy_iter {
            policies.push(policy?);
        }

        Ok(policies)
    }

    /// 根据ID获取招募政策
    pub fn get_policy_by_id(
        &self,
        policy_id: i64,
    ) -> Result<Option<RecruitmentPolicyWithCompany>, Box<dyn Error>> {
        let conn = self.get_connection()?;

        let result = conn.query_row(
            "SELECT
                rcp.policy_id, rcp.project_id, rcp.recruitment_company_item_id, rcp.policy_name,
                rcp.informed_consent_fee, rcp.randomization_fee, rcp.fee_currency,
                rcp.payment_method, rcp.description, rcp.is_active, rcp.notes,
                rcp.created_at, rcp.updated_at,
                di.item_id, di.item_key, di.item_value, di.item_description
             FROM recruitment_company_policies rcp
             LEFT JOIN dictionary_items di ON rcp.recruitment_company_item_id = di.item_id
             WHERE rcp.policy_id = ?",
            params![policy_id],
            |row| {
                let policy = RecruitmentPolicy {
                    policy_id: row.get(0)?,
                    project_id: row.get(1)?,
                    recruitment_company_item_id: row.get(2)?,
                    policy_name: row.get(3)?,
                    informed_consent_fee: row.get(4)?,
                    randomization_fee: row.get(5)?,
                    fee_currency: row.get(6)?,
                    payment_method: row.get(7)?,
                    description: row.get(8)?,
                    is_active: row.get(9)?,
                    notes: row.get(10)?,
                    created_at: row.get(11)?,
                    updated_at: row.get(12)?,
                };

                let company = if let Ok(item_id) = row.get::<_, i64>(13) {
                    Some(DictionaryItem {
                        item_id,
                        item_key: row.get(14)?,
                        item_value: row.get(15)?,
                        item_description: row.get(16)?,
                    })
                } else {
                    None
                };

                Ok(RecruitmentPolicyWithCompany::new(policy, company))
            },
        );

        match result {
            Ok(policy) => Ok(Some(policy)),
            Err(rusqlite::Error::QueryReturnedNoRows) => Ok(None),
            Err(e) => Err(Box::new(e)),
        }
    }

    /// 创建招募政策
    pub fn create_policy(
        &self,
        request: &CreateRecruitmentPolicyRequest,
    ) -> Result<i64, Box<dyn Error>> {
        let conn = self.get_connection()?;

        // 验证费用逻辑
        let policy = RecruitmentPolicy::new(request.clone());
        policy.validate_fees()?;

        // 注意：允许同一项目的同一家招募公司有多个政策
        // 这样可以支持不同时期、不同类型的政策设置

        let result = conn.execute(
            "INSERT INTO recruitment_company_policies (
                project_id, recruitment_company_item_id, policy_name, informed_consent_fee,
                randomization_fee, fee_currency, payment_method, description,
                is_active, notes, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'), datetime('now'))",
            params![
                request.project_id,
                request.recruitment_company_item_id,
                request.policy_name,
                request.informed_consent_fee,
                request.randomization_fee,
                request.fee_currency.as_deref().unwrap_or("CNY"),
                request.payment_method,
                request.description,
                true,
                request.notes
            ],
        )?;

        if result > 0 {
            let policy_id = conn.last_insert_rowid();
            info!("创建招募政策成功，ID: {}", policy_id);
            Ok(policy_id)
        } else {
            Err("创建招募政策失败".into())
        }
    }

    /// 更新招募政策
    pub fn update_policy(
        &self,
        policy_id: i64,
        request: &UpdateRecruitmentPolicyRequest,
    ) -> Result<(), Box<dyn Error>> {
        let conn = self.get_connection()?;

        // 获取现有政策
        let mut existing_policy = self.get_policy_by_id(policy_id)?
            .ok_or("招募政策不存在")?
            .policy;

        // 应用更新
        existing_policy.update(request.clone());

        // 验证更新后的费用逻辑
        existing_policy.validate_fees()?;

        // 注意：允许同一项目的同一家招募公司有多个政策
        // 更新时不需要检查重复性，因为业务允许多个政策共存

        let mut sql = String::from("UPDATE recruitment_company_policies SET updated_at = datetime('now')");
        let mut params: Vec<Box<dyn rusqlite::ToSql>> = Vec::new();

        if let Some(company_id) = request.recruitment_company_item_id {
            sql.push_str(", recruitment_company_item_id = ?");
            params.push(Box::new(company_id));
        }

        if let Some(policy_name) = &request.policy_name {
            sql.push_str(", policy_name = ?");
            params.push(Box::new(policy_name.clone()));
        }

        if let Some(informed_fee) = request.informed_consent_fee {
            sql.push_str(", informed_consent_fee = ?");
            params.push(Box::new(informed_fee));
        }

        if let Some(random_fee) = request.randomization_fee {
            sql.push_str(", randomization_fee = ?");
            params.push(Box::new(random_fee));
        }

        if let Some(currency) = &request.fee_currency {
            sql.push_str(", fee_currency = ?");
            params.push(Box::new(currency.clone()));
        }

        if let Some(payment) = &request.payment_method {
            sql.push_str(", payment_method = ?");
            params.push(Box::new(payment.clone()));
        }

        if let Some(desc) = &request.description {
            sql.push_str(", description = ?");
            params.push(Box::new(desc.clone()));
        }

        if let Some(notes) = &request.notes {
            sql.push_str(", notes = ?");
            params.push(Box::new(notes.clone()));
        }

        if let Some(is_active) = request.is_active {
            sql.push_str(", is_active = ?");
            params.push(Box::new(is_active));
        }

        sql.push_str(" WHERE policy_id = ?");
        params.push(Box::new(policy_id));

        let param_refs: Vec<&dyn rusqlite::ToSql> = params.iter().map(|p| p.as_ref()).collect();
        let result = conn.execute(&sql, &param_refs[..])?;

        if result > 0 {
            info!("更新招募政策成功，ID: {}", policy_id);
            Ok(())
        } else {
            Err("更新招募政策失败".into())
        }
    }

    /// 删除招募政策（软删除）
    pub fn delete_policy(&self, policy_id: i64) -> Result<(), Box<dyn Error>> {
        let conn = self.get_connection()?;

        let result = conn.execute(
            "UPDATE recruitment_company_policies
             SET is_active = 0, updated_at = datetime('now')
             WHERE policy_id = ?",
            params![policy_id],
        )?;

        if result > 0 {
            info!("删除招募政策成功，ID: {}", policy_id);
            Ok(())
        } else {
            Err("删除招募政策失败".into())
        }
    }

    /// 硬删除招募政策
    pub fn hard_delete_policy(&self, policy_id: i64) -> Result<(), Box<dyn Error>> {
        let conn = self.get_connection()?;

        let result = conn.execute(
            "DELETE FROM recruitment_company_policies WHERE policy_id = ?",
            params![policy_id],
        )?;

        if result > 0 {
            info!("硬删除招募政策成功，ID: {}", policy_id);
            Ok(())
        } else {
            Err("硬删除招募政策失败".into())
        }
    }

    /// 获取招募公司列表（从字典表）
    pub fn get_recruitment_companies(&self) -> Result<Vec<DictionaryItem>, Box<dyn Error>> {
        let conn = self.get_connection()?;

        let mut stmt = conn.prepare(
            "SELECT di.item_id, di.item_key, di.item_value, di.item_description
             FROM dictionary_items di
             JOIN dictionaries d ON di.dictionary_id = d.id
             WHERE d.name = '招募公司' AND di.status = 'active'
             ORDER BY di.item_value",
        )?;

        let company_iter = stmt.query_map([], |row| {
            Ok(DictionaryItem {
                item_id: row.get(0)?,
                item_key: row.get(1)?,
                item_value: row.get(2)?,
                item_description: row.get(3)?,
            })
        })?;

        let mut companies = Vec::new();
        for company in company_iter {
            companies.push(company?);
        }

        info!("获取到 {} 个招募公司", companies.len());
        Ok(companies)
    }

    /// 获取招募政策概览（包含项目信息）
    pub fn get_policies_overview(
        &self,
        query: &RecruitmentPolicyOverviewQuery,
    ) -> Result<RecruitmentPolicyOverviewPagination, Box<dyn Error>> {
        let conn = self.get_connection()?;

        // 构建基础查询
        let mut sql = String::from(
            "SELECT
                rcp.policy_id, rcp.project_id, rcp.recruitment_company_item_id, rcp.policy_name,
                rcp.informed_consent_fee, rcp.randomization_fee, rcp.fee_currency,
                rcp.payment_method, rcp.description, rcp.is_active, rcp.notes,
                rcp.created_at, rcp.updated_at,
                -- 招募公司信息
                company.item_id as company_item_id, company.item_key as company_key,
                company.item_value as company_name, company.item_description as company_desc,
                -- 项目信息
                p.project_name, p.project_short_name,
                -- 疾病信息
                disease.item_id as disease_item_id, disease.item_key as disease_key,
                disease.item_value as disease_name, disease.item_description as disease_desc,
                -- 项目阶段信息
                stage.item_id as stage_item_id, stage.item_key as stage_key,
                stage.item_value as stage_name, stage.item_description as stage_desc,
                -- 项目状态信息
                status.item_id as status_item_id, status.item_key as status_key,
                status.item_value as status_name, status.item_description as status_desc,
                -- 招募状态信息
                recruit.item_id as recruit_item_id, recruit.item_key as recruit_key,
                recruit.item_value as recruit_name, recruit.item_description as recruit_desc
             FROM recruitment_company_policies rcp
             LEFT JOIN dictionary_items company ON rcp.recruitment_company_item_id = company.item_id
             LEFT JOIN projects p ON rcp.project_id = p.project_id
             LEFT JOIN dictionary_items disease ON p.disease_item_id = disease.item_id
             LEFT JOIN dictionary_items stage ON p.project_stage_item_id = stage.item_id
             LEFT JOIN dictionary_items status ON p.project_status_item_id = status.item_id
             LEFT JOIN dictionary_items recruit ON p.recruitment_status_item_id = recruit.item_id
             WHERE 1=1",
        );

        let mut params: Vec<Box<dyn rusqlite::ToSql>> = Vec::new();

        // 添加筛选条件
        if let Some(stage_id) = query.project_stage_item_id {
            sql.push_str(" AND p.project_stage_item_id = ?");
            params.push(Box::new(stage_id));
        }

        if let Some(disease_id) = query.disease_item_id {
            sql.push_str(" AND p.disease_item_id = ?");
            params.push(Box::new(disease_id));
        }

        if let Some(company_id) = query.recruitment_company_item_id {
            sql.push_str(" AND rcp.recruitment_company_item_id = ?");
            params.push(Box::new(company_id));
        }

        if let Some(is_active) = query.is_active {
            sql.push_str(" AND rcp.is_active = ?");
            params.push(Box::new(is_active));
        }

        // 计算总数
        let count_sql = format!("SELECT COUNT(*) FROM ({}) as count_query", sql);
        let param_refs: Vec<&dyn rusqlite::ToSql> = params.iter().map(|p| p.as_ref()).collect();
        let total: i64 = conn.query_row(&count_sql, &param_refs[..], |row| row.get(0))?;

        // 添加排序
        let sort_by = query.sort_by.as_deref().unwrap_or("p.project_name");
        let sort_order = query.sort_order.as_deref().unwrap_or("asc");
        sql.push_str(&format!(" ORDER BY {} {}", sort_by, sort_order));

        // 添加分页
        let page = query.page.unwrap_or(1);
        let page_size = query.page_size.unwrap_or(20);
        let offset = (page - 1) * page_size;
        sql.push_str(&format!(" LIMIT {} OFFSET {}", page_size, offset));

        let mut stmt = conn.prepare(&sql)?;
        let param_refs: Vec<&dyn rusqlite::ToSql> = params.iter().map(|p| p.as_ref()).collect();

        let policy_iter = stmt.query_map(&param_refs[..], |row| {
            let policy = RecruitmentPolicy {
                policy_id: row.get(0)?,
                project_id: row.get(1)?,
                recruitment_company_item_id: row.get(2)?,
                policy_name: row.get(3)?,
                informed_consent_fee: row.get(4)?,
                randomization_fee: row.get(5)?,
                fee_currency: row.get(6)?,
                payment_method: row.get(7)?,
                description: row.get(8)?,
                is_active: row.get(9)?,
                notes: row.get(10)?,
                created_at: row.get(11)?,
                updated_at: row.get(12)?,
            };

            let company = if let Ok(item_id) = row.get::<_, i64>(13) {
                Some(DictionaryItem {
                    item_id,
                    item_key: row.get(14)?,
                    item_value: row.get(15)?,
                    item_description: row.get(16)?,
                })
            } else {
                None
            };

            let disease = if let Ok(item_id) = row.get::<_, i64>(19) {
                Some(DictionaryItem {
                    item_id,
                    item_key: row.get(20)?,
                    item_value: row.get(21)?,
                    item_description: row.get(22)?,
                })
            } else {
                None
            };

            let project_stage = if let Ok(item_id) = row.get::<_, i64>(23) {
                Some(DictionaryItem {
                    item_id,
                    item_key: row.get(24)?,
                    item_value: row.get(25)?,
                    item_description: row.get(26)?,
                })
            } else {
                None
            };

            let project_status = if let Ok(item_id) = row.get::<_, i64>(27) {
                Some(DictionaryItem {
                    item_id,
                    item_key: row.get(28)?,
                    item_value: row.get(29)?,
                    item_description: row.get(30)?,
                })
            } else {
                None
            };

            let recruitment_status = if let Ok(item_id) = row.get::<_, i64>(31) {
                Some(DictionaryItem {
                    item_id,
                    item_key: row.get(32)?,
                    item_value: row.get(33)?,
                    item_description: row.get(34)?,
                })
            } else {
                None
            };

            Ok(RecruitmentPolicyWithProject {
                policy,
                company,
                project_name: row.get(17)?,
                project_short_name: row.get(18)?,
                disease,
                project_stage,
                project_status,
                recruitment_status,
            })
        })?;

        let mut items = Vec::new();
        for policy in policy_iter {
            items.push(policy?);
        }

        info!("获取到 {} 个招募政策概览记录，总数: {}", items.len(), total);
        Ok(RecruitmentPolicyOverviewPagination {
            items,
            total,
            page,
            page_size,
        })
    }
}
