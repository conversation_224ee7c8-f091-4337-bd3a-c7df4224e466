// Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
use log::{debug, error, info, warn, LevelFilter};
use reqwest::header::{HeaderMap, AUTHORIZATION, CONTENT_TYPE, USER_AGENT};
use serde::{Deserialize, Serialize};
use tauri::{
    menu::{MenuBuilder, MenuItemBuilder, PredefinedMenuItem, SubmenuBuilder},
    Manager,
};
use tauri_plugin_dialog;

// 导入模块
pub mod commands;
pub mod config;
pub mod db;
pub mod error;
pub mod models;
pub mod repositories;
pub mod response;
pub mod services;
pub mod staff;

// Notion API 返回错误时的结构体
#[derive(Serialize, Deserialize, Debug)]
struct NotionErrorResponse {
    object: String,
    status: u16,
    code: String,
    message: String,
}

// 最终返回给前端的数据项结构体
#[derive(Serialize, Deserialize, Debug)]
struct NotionDatabaseItem {
    id: String,
    title: String,  // 标题
    status: String, // 状态
    #[serde(rename = "dueDate", skip_serializing_if = "Option::is_none")]
    // 匹配 TS 接口，如果为 None 则忽略
    due_date: Option<String>, // 截止日期 (可选)
    #[serde(skip_serializing_if = "Option::is_none")] // 如果为 None 则忽略
    priority: Option<Vec<String>>, // 优先级 (可选, 多选)
}

// Notion API 查询数据库成功响应的顶层结构体
#[derive(Serialize, Deserialize, Debug)]
struct NotionQueryResponse {
    object: String,
    results: Vec<NotionPage>, // 页面结果列表
    #[serde(rename = "next_cursor")]
    next_cursor: Option<String>, // 用于分页的游标
    #[serde(rename = "has_more")]
    has_more: bool, // 是否还有更多数据
}

// Notion 页面的结构体
#[derive(Serialize, Deserialize, Debug)]
struct NotionPage {
    object: String,
    id: String,
    properties: serde_json::Value, // 使用 Value 来灵活处理不同结构的属性
                                   // 可以根据需要添加其他 Page 字段，例如 url, created_time 等
                                   // url: Option<String>,
                                   // created_time: Option<String>,
                                   // last_edited_time: Option<String>,
}

// 简单的 Tauri 命令示例
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

// 定义 Tauri 命令：获取 Notion 数据库数据
#[tauri::command]
async fn fetch_notion_database(
    api_key: String,
    database_id: String,
) -> Result<Vec<NotionDatabaseItem>, String> {
    // 打印开始日志，包含数据库 ID
    println!(
        "Rust: 开始获取 Notion 数据库项目。 Database ID: {}",
        database_id
    );

    // 基本的 API Key 格式验证
    if !api_key.starts_with("secret_") && !api_key.starts_with("ntn_") {
        return Err("无效的Notion API密钥格式。".into());
    }

    // 格式化数据库 ID (移除可能存在的 '-')
    let formatted_db_id = database_id.replace("-", "");
    // 构建 Notion API 请求 URL
    let url = format!(
        "https://api.notion.com/v1/databases/{}/query",
        formatted_db_id
    );
    println!("Rust: 请求 URL: {}", url);

    // 设置 HTTP 请求头
    let mut headers = HeaderMap::new();
    headers.insert(
        AUTHORIZATION,
        format!("Bearer {}", api_key)
            .parse()
            .map_err(|e| format!("无法解析 Authorization header: {}", e))?,
    );
    headers.insert(
        "Notion-Version",
        "2022-06-28"
            .parse()
            .map_err(|e| format!("无法解析 Notion-Version header: {}", e))?,
    );
    headers.insert(
        CONTENT_TYPE,
        "application/json"
            .parse()
            .map_err(|e| format!("无法解析 Content-Type header: {}", e))?,
    );
    // 添加 User-Agent 是一个好习惯
    headers.insert(
        USER_AGENT,
        "TauriApp/1.0"
            .parse()
            .map_err(|e| format!("无法解析 User-Agent header: {}", e))?,
    );

    // 创建 reqwest HTTP 客户端
    let client = reqwest::Client::new();
    // 发送 POST 请求到 Notion API
    let response = client
        .post(&url)
        .headers(headers)
        // 设置请求体，这里指定了 page_size，可以根据需要添加 filter 或 sorts
        .json(&serde_json::json!({ "page_size": 100 }))
        .send()
        .await; // 等待异步请求完成

    // 处理请求结果
    match response {
        Ok(res) => {
            // 获取 HTTP 状态码
            let status = res.status();
            println!("Rust: API 响应状态: {}", status);
            // 检查状态码是否表示成功 (2xx)
            if status.is_success() {
                // 尝试将成功的响应体解析为 NotionQueryResponse 结构体
                match res.json::<NotionQueryResponse>().await {
                    Ok(data) => {
                        // 解析成功，打印获取到的结果数量
                        println!(
                            "Rust: 成功获取并解析数据，共 {} 条结果。",
                            data.results.len()
                        );
                        // 遍历结果列表 (Vec<NotionPage>)，并将每个页面映射为 NotionDatabaseItem
                        let items = data
                            .results
                            .into_iter()
                            .map(|page| {
                                // 获取页面的属性 (serde_json::Value)
                                let props = page.properties;

                                // --- 修改开始: 使用中文属性名提取数据 ---

                                // 提取标题 ("项目名称"，类型: title)
                                let title = props
                                    .get("项目名称") // <-- 修改: 使用中文属性名
                                    .and_then(|n| n.get("title")) // 获取 title 数组
                                    .and_then(|t| t.get(0)) // 获取数组的第一个元素
                                    .and_then(|t0| t0.get("plain_text")) // 获取纯文本内容
                                    .and_then(|pt| pt.as_str()) // 转换为 &str
                                    .unwrap_or("无标题") // 如果提取失败，使用默认值
                                    .to_string(); // 转换为 String

                                // 提取状态 ("项目状态"，类型: select)
                                let status = props
                                    .get("项目状态") // <-- 修改: 使用中文属性名
                                    .and_then(|s| s.get("select")) // <-- 修改: 路径改为 select (因为类型是 select)
                                    .and_then(|st| st.get("name")) // 获取 select 选项的名称
                                    .and_then(|n| n.as_str()) // 转换为 &str
                                    .unwrap_or("未设置") // 如果提取失败，使用默认值
                                    .to_string(); // 转换为 String

                                // 提取截止日期 ("开始日期"，类型: date)
                                let due_date = props
                                    .get("开始日期") // <-- 修改: 使用中文属性名
                                    .and_then(|d| d.get("date")) // 获取 date 对象
                                    .and_then(|dt| dt.get("start")) // 获取 date 对象中的 start 字段
                                    .and_then(|s| s.as_str()) // 转换为 &str
                                    .map(String::from); // 转换为 Option<String>

                                // 提取优先级 ("优先级"，类型: multi_select) - 假设可能存在名为"优先级"的多选属性
                                let priority = props
                                    .get("优先级") // <-- 修改: 尝试使用中文名 (如果不存在则为 None)
                                    .and_then(|p| p.get("multi_select")) // 获取 multi_select 数组
                                    .and_then(|ms| ms.as_array()) // 转换为 JSON 数组
                                    .map(|arr| {
                                        // 如果数组存在
                                        arr.iter() // 遍历数组中的每个选项
                                        .filter_map(|item| item.get("name").and_then(|n| n.as_str())) // 提取选项的 name
                                        .map(String::from) // 转换为 String
                                        .collect::<Vec<String>>() // 收集为 Vec<String>
                                    }); // 结果是 Option<Vec<String>>

                                // --- 修改结束 ---

                                // 组装成最终返回给前端的 NotionDatabaseItem 结构
                                NotionDatabaseItem {
                                    id: page.id, // 页面 ID
                                    title,       // 提取到的标题
                                    status,      // 提取到的状态
                                    due_date,    // 提取到的截止日期 (Option<String>)
                                    priority,    // 提取到的优先级 (Option<Vec<String>>)
                                }
                            })
                            .collect(); // 将所有 NotionDatabaseItem 收集到 Vec 中
                        Ok(items) // 返回包含结果列表的 Ok
                    }
                    Err(e) => {
                        // 如果 JSON 解析失败，记录错误并返回错误信息
                        eprintln!("Rust: 解析 Notion 响应 JSON 失败: {}", e);
                        Err(format!("解析 Notion 响应失败: {}", e))
                    }
                }
            } else {
                // 如果 HTTP 状态码不是成功 (例如 4xx, 5xx)
                // 尝试读取错误响应体
                let error_text = res
                    .text()
                    .await
                    .unwrap_or_else(|_| "无法读取错误响应体".to_string());
                eprintln!("Rust: Notion API 错误响应体: {}", error_text);
                // 尝试将错误响应体解析为 Notion 定义的错误结构
                let error_message = match serde_json::from_str::<NotionErrorResponse>(&error_text) {
                    Ok(notion_error) => format!(
                        "Notion API 错误 ({} {}): {}",
                        status, notion_error.code, notion_error.message
                    ), // 格式化 Notion 错误信息
                    Err(_) => format!(
                        "Notion API 请求失败，状态码: {}。响应体: {}",
                        status, error_text
                    ), // 如果无法解析为 Notion 错误结构，返回通用错误
                };
                Err(error_message) // 返回错误信息
            }
        }
        Err(e) => {
            // 如果请求本身失败 (例如网络错误)
            eprintln!("Rust: 请求 Notion API 失败: {}", e);
            Err(format!("请求 Notion API 失败: {}", e)) // 返回错误信息
        }
    }
}

// 同步笔记到 Notion 页面
#[tauri::command]
async fn sync_note_to_notion(
    api_key: String,
    page_id: String,
    content: String,
) -> Result<String, String> {
    println!("Rust: 开始同步笔记到 Notion 页面。Page ID: {}", page_id);

    // 基本的 API Key 格式验证
    if !api_key.starts_with("secret_") && !api_key.starts_with("ntn_") {
        return Err("无效的Notion API密钥格式。".into());
    }

    // 格式化页面 ID (移除可能存在的 '-')
    let formatted_page_id = page_id.replace("-", "");
    // 构建 Notion API 请求 URL
    let url = format!(
        "https://api.notion.com/v1/blocks/{}/children",
        formatted_page_id
    );
    println!("Rust: 请求 URL: {}", url);

    // 设置 HTTP 请求头
    let mut headers = HeaderMap::new();
    headers.insert(
        AUTHORIZATION,
        format!("Bearer {}", api_key)
            .parse()
            .map_err(|e| format!("无法解析 Authorization header: {}", e))?,
    );
    headers.insert(
        "Notion-Version",
        "2022-06-28"
            .parse()
            .map_err(|e| format!("无法解析 Notion-Version header: {}", e))?,
    );
    headers.insert(
        CONTENT_TYPE,
        "application/json"
            .parse()
            .map_err(|e| format!("无法解析 Content-Type header: {}", e))?,
    );
    // 添加 User-Agent 是一个好习惯
    headers.insert(
        USER_AGENT,
        "TauriApp/1.0"
            .parse()
            .map_err(|e| format!("无法解析 User-Agent header: {}", e))?,
    );

    // 创建 reqwest HTTP 客户端
    let client = reqwest::Client::new();

    // 构建请求体 - 将笔记内容添加为段落块
    let request_body = serde_json::json!({
        "children": [
            {
                "object": "block",
                "type": "paragraph",
                "paragraph": {
                    "rich_text": [
                        {
                            "type": "text",
                            "text": {
                                "content": content
                            }
                        }
                    ]
                }
            }
        ]
    });

    // 发送 PATCH 请求到 Notion API
    let response = client
        .patch(&url)
        .headers(headers)
        .json(&request_body)
        .send()
        .await; // 等待异步请求完成

    // 处理请求结果
    match response {
        Ok(res) => {
            // 获取 HTTP 状态码
            let status = res.status();
            println!("Rust: API 响应状态: {}", status);
            // 检查状态码是否表示成功 (2xx)
            if status.is_success() {
                println!("Rust: 成功同步笔记到 Notion 页面");
                Ok("成功同步笔记到 Notion 页面".to_string())
            } else {
                // 如果 HTTP 状态码不是成功 (例如 4xx, 5xx)
                // 尝试读取错误响应体
                let error_text = res
                    .text()
                    .await
                    .unwrap_or_else(|_| "无法读取错误响应体".to_string());
                eprintln!("Rust: Notion API 错误响应体: {}", error_text);
                // 尝试将错误响应体解析为 Notion 定义的错误结构
                let error_message = match serde_json::from_str::<NotionErrorResponse>(&error_text) {
                    Ok(notion_error) => format!(
                        "Notion API 错误 ({} {}): {}",
                        status, notion_error.code, notion_error.message
                    ), // 格式化 Notion 错误信息
                    Err(_) => format!(
                        "Notion API 请求失败，状态码: {}。响应体: {}",
                        status, error_text
                    ), // 如果无法解析为 Notion 错误结构，返回通用错误
                };
                Err(error_message) // 返回错误信息
            }
        }
        Err(e) => {
            // 如果请求本身失败 (例如网络错误)
            eprintln!("Rust: 请求 Notion API 失败: {}", e);
            Err(format!("请求 Notion API 失败: {}", e)) // 返回错误信息
        }
    }
}

// Tauri 应用的入口点
#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // 注意: 不再在这里初始化 env_logger，而是使用 tauri_plugin_log
    // 日志初始化将由 tauri_plugin_log 插件处理

    // 初始化 SQLite 服务
    let db_dir = std::path::PathBuf::from("/Users/<USER>/我的文档/sqlite");

    // 确保目录存在
    if !db_dir.exists() {
        std::fs::create_dir_all(&db_dir).expect("无法创建数据库目录");
    }

    let db_path = db_dir.join("peckbyte.db").to_string_lossy().to_string();

    // 初始化数据库连接路径
    db::init_db_path(db_dir.join("peckbyte.db"));

    // 初始化 staff 表
    if let Ok(conn) = db::get_connection() {
        if let Err(e) = staff::init_staff_table(&conn) {
            eprintln!("初始化 staff 表失败: {}", e);
        } else {
            println!("staff 表初始化成功");
        }
    }

    match services::sqlite_service::init_sqlite_service(&db_path) {
        Ok(_) => {
            println!("SQLite 服务初始化成功: {}", db_path);
            // 获取 SQLite 服务实例
            match services::sqlite_service::SQLITE_SERVICE.lock() {
                Ok(service_guard) => {
                    if let Some(service) = &*service_guard {
                        // 初始化 SQLite 字典服务
                        match services::sqlite_dictionary_service::init_sqlite_dictionary_service(
                            std::sync::Arc::new(service.clone()),
                        ) {
                            Ok(_) => println!("SQLite 字典服务初始化成功"),
                            Err(e) => eprintln!("SQLite 字典服务初始化失败: {}", e),
                        }
                    }
                }
                Err(e) => eprintln!("获取 SQLite 服务实例失败: {}", e),
            }
        }
        Err(e) => eprintln!("SQLite 服务初始化失败: {}", e),
    }
    // 使用 Tauri Builder 构建应用
    tauri::Builder::default()
        .plugin(tauri_plugin_http::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(
            tauri_plugin_log::Builder::new()
                .level(log::LevelFilter::Debug)
                .build(),
        )
        // 初始化插件 (例如用于打开链接)
        .plugin(tauri_plugin_opener::init())
        // 初始化剪贴板插件
        .plugin(tauri_plugin_clipboard_manager::init())
        // 初始化 dialog 插件
        .plugin(tauri_plugin_dialog::init())
        // 设置应用菜单
        .setup(|app: &mut tauri::App| {
            // 创建主菜单（About）
            let main_menu_submenu = SubmenuBuilder::new(app, "PeckByte")
                .text("about", "关于")
                .build()?;

            // 创建编辑菜单（包含复制粘贴等快捷键）
            let edit_submenu = SubmenuBuilder::new(app, "编辑")
                .item(&PredefinedMenuItem::undo(app, Some("撤销"))?)
                .item(&PredefinedMenuItem::redo(app, Some("重做"))?)
                .separator()
                .item(&PredefinedMenuItem::cut(app, Some("剪切"))?)
                .item(&PredefinedMenuItem::copy(app, Some("复制"))?)
                .item(&PredefinedMenuItem::paste(app, Some("粘贴"))?)
                .item(&PredefinedMenuItem::select_all(app, Some("全选"))?)
                .build()?;

            // 创建项目管理子菜单
            let project_management_submenu = SubmenuBuilder::new(app, "项目管理")
                .text("project_dashboard", "项目仪表盘")
                .text("project_list", "项目列表")
                .text("new_project", "新建项目")
                .separator()
                .text("rule_definitions", "入排标准规则管理")
                .text("auto_criteria", "自动生成入排标准")
                .build()?;

            // 创建工具子菜单
            let tools_submenu = SubmenuBuilder::new(app, "工具")
                .text("inspiration_notes", "灵感笔记")
                .text("lighthouse_users", "Lighthouse-用户管理")
                .text("referrer_management", "推荐人管理")
                .text("recruitment_overview", "招募政策概览")
                .build()?;

            // 创建系统设置子菜单
            let settings_submenu = SubmenuBuilder::new(app, "系统设置")
                .text("system_settings", "系统配置")
                .separator()
                .text("dictionary_list", "SQLite字典管理")
                .text("staff_list", "人员管理")
                .build()?;

            // 创建主菜单
            let menu = MenuBuilder::new(app)
                .item(&main_menu_submenu)
                .item(&edit_submenu)
                .item(&project_management_submenu)
                .item(&tools_submenu)
                .item(&settings_submenu)
                .build()?;

            // 设置应用菜单
            app.set_menu(menu)?;

            // 处理菜单事件
            app.on_menu_event(move |app_handle, event| {
                let window = app_handle.get_webview_window("main");
                if let Some(window) = window {
                    match event.id().0.as_str() {
                        // 主菜单项
                        "about" => {
                            let _ = window.eval("window.location.href = '/about'");
                        }

                        // 编辑菜单项
                        "undo" => {
                            let _ = window.eval("document.execCommand('undo')");
                        }
                        "redo" => {
                            let _ = window.eval("document.execCommand('redo')");
                        }
                        "cut" => {
                            let _ = window.eval("document.execCommand('cut')");
                        }
                        "copy" => {
                            let _ = window.eval("document.execCommand('copy')");
                        }
                        "paste" => {
                            let _ = window.eval("document.execCommand('paste')");
                        }
                        "selectAll" => {
                            let _ = window.eval("document.execCommand('selectAll')");
                        }

                        // 项目管理菜单项
                        "project_dashboard" => {
                            let _ = window.eval("window.location.href = '/dashboard/project-management'");
                        }
                        "project_list" => {
                            let _ = window.eval("window.location.href = '/projects'");
                        }
                        "new_project" => {
                            let _ = window.eval("window.location.href = '/projects/new'");
                        }
                        "rule_definitions" => {
                            let _ = window.eval("window.location.href = '/rules/definitions'");
                        }
                        "auto_criteria" => {
                            let _ = window.eval("window.location.href = '/projects/auto-criteria'");
                        }

                        // 数据字典菜单项
                        "dictionary_list" => {
                            let _ = window.eval("window.location.href = '/sqlite-dictionaries'");
                        }

                        // 人员管理菜单项
                        "staff_list" => {
                            let _ = window.eval("window.location.href = '/staff'");
                        }

                        // 工具菜单项
                        "inspiration_notes" => {
                            let _ = window.eval("window.location.href = '/notes'");
                        },
                        "lighthouse_users" => {
                            let _ = window.eval("window.location.href = '/lighthouse-users'");
                        },
                        "referrer_management" => {
                            let _ = window.eval("window.location.href = '/referrer-management'");
                        },
                        "recruitment_overview" => {
                            let _ = window.eval("window.location.href = '/recruitment-overview'");
                        }

                        // 系统设置菜单项
                        "system_settings" => {
                            let _ = window.eval("window.location.href = '/settings'");
                        }

                        _ => {}
                    }
                }
            });

            Ok(())
        })
        // 注册 Tauri 命令，使其可以被前端调用
        .invoke_handler(tauri::generate_handler![
            greet,
            fetch_notion_database,
            sync_note_to_notion,
            commands::sync_projects_to_notion,
            commands::get_all_projects,
            commands::get_projects_paginated,
            commands::get_project_by_id,
            commands::create_project,
            commands::update_project,
            commands::delete_project,
            commands::get_system_config,
            commands::save_system_config,
            commands::get_config_list,
            commands::save_config_list,
            commands::get_all_configs,
            commands::get_config,
            commands::save_config,
            commands::delete_config,
            commands::get_files,
            commands::open_file,
            commands::open_folder,
            commands::show_open_dialog,
            commands::show_save_dialog,
            commands::create_file,
            commands::read_file,
            commands::delete_file,
            commands::create_directory,
            commands::copy_file,
            commands::move_file,
            commands::rename_file,
            commands::get_file_info,
            commands::check_missing_tags,
            commands::get_tag_rules,
            commands::export_projects_to_folder,
            commands::add_tag_rule,
            commands::delete_tag_rule,
            commands::update_tag_rule,
            commands::sqlite_get_all_dicts,
            commands::sqlite_query_dicts,
            commands::sqlite_get_dict_by_id,
            commands::sqlite_get_dict_by_name,
            commands::sqlite_create_dict,
            commands::sqlite_update_dict,
            commands::sqlite_delete_dict,
            commands::sqlite_get_dict_items,
            commands::sqlite_add_dict_item,
            commands::sqlite_update_dict_item,
            commands::sqlite_delete_dict_item,
            commands::sqlite_test_command,
            // 项目管理命令
            commands::get_projects_list,
            commands::get_project_details,
            commands::pm_create_project,
            commands::pm_update_project,
            commands::save_project_with_details,
            commands::pm_delete_project,
            commands::init_project_management_tables,
            commands::check_database_tables,
            commands::reset_database_tables,
            // 规则设计器命令
            commands::init_rule_designer_tables,
            commands::get_rule_definitions,
            commands::get_rule_definition_by_id,
            commands::create_rule_definition,
            commands::update_rule_definition,
            commands::delete_rule_definition,
            commands::find_projects_using_rule,
            commands::get_project_criteria,
            commands::get_project_criterion_by_id,
            commands::create_project_criterion,
            commands::update_project_criterion,
            commands::delete_project_criterion,
            // 招募政策命令
            commands::init_recruitment_policy_tables,
            commands::migrate_recruitment_policy_tables,
            commands::get_project_recruitment_policies,
            commands::get_recruitment_policies,
            commands::get_recruitment_policy_by_id,
            commands::create_recruitment_policy,
            commands::update_recruitment_policy,
            commands::delete_recruitment_policy,
            commands::hard_delete_recruitment_policy,
            commands::get_recruitment_companies,
            commands::batch_create_recruitment_policies,
            commands::batch_delete_recruitment_policies,
            commands::get_recruitment_policies_overview,
            // 仪表盘命令
            commands::get_dashboard_overview,
            commands::get_project_status_distribution,
            commands::get_project_status_chart_data,
            commands::get_project_stage_distribution,
            commands::get_project_stage_chart_data,
            commands::get_recruitment_status_distribution,
            commands::get_recruitment_status_chart_data,
            commands::get_disease_distribution,
            commands::get_disease_chart_data,
            commands::get_sponsor_distribution,
            commands::get_sponsor_chart_data,
            commands::get_monthly_new_projects,
            commands::get_monthly_new_projects_chart_data,
            commands::debug_project_date_statistics,
            commands::get_financial_metrics,
            commands::get_personnel_metrics,
            commands::get_timeline_metrics,
            staff::get_all_staff,
            staff::query_staff,
            staff::get_staff_by_id,
            staff::create_staff,
            staff::update_staff,
            staff::delete_staff,
            staff::get_positions,
            // Lighthouse 用户管理命令
            commands::test_lighthouse_connection,
            commands::lighthouse_login,
            commands::get_lighthouse_users,
            commands::get_lighthouse_user_by_id,
            commands::create_lighthouse_user,
            commands::update_lighthouse_user,
            commands::delete_lighthouse_user,
            // CSV导入命令
            commands::parse_csv_personnel_data,
            commands::execute_csv_personnel_import,
            commands::perform_project_quality_control,
            commands::get_csv_import_template,
            commands::validate_csv_format,
            commands::get_import_history,
            commands::batch_delete_personnel_assignments,
            commands::get_project_personnel_statistics
        ])
        // 运行 Tauri 应用
        .run(tauri::generate_context!())
        // 如果启动失败，则 panic
        .expect("error while running tauri application");
}
